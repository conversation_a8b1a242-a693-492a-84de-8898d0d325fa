import { RADIUS_POINTER, NHIETS, MACHS } from "utils/vital-signs/constants";
import { clamp, isEmpty } from "lodash";
import { t } from "i18next";
import moment from "moment";

import { LOAI_DICH_VU, TRO_THO } from "constants/index";
import { isArray } from "utils/index";

export const drawLine = (ctx, from, to, width, lighdash = [], color) => {
  try {
    ctx.save(); // Lưu trạng thái hiện tại
    ctx.lineWidth = width;
    if (lighdash) {
      ctx.setLineDash(lighdash);
    } else ctx.setLineDash([]);
    ctx.beginPath();
    ctx.moveTo(from.x, from.y);
    ctx.lineTo(to.x, to.y);
    ctx.fillStyle = "black";
    if (color) {
      ctx.strokeStyle = color;
    }
    ctx.stroke();
    ctx.restore(); // <PERSON>h<PERSON><PERSON> phục trạng thái ban đầu
  } catch (error) {
    console.log(error);
  }
};

export const drawPoint = ({
  ctx,
  index,
  point,
  type,
  columnWidth,
  leftColumnWidth,
  item,
}) => {
  ctx.save(); // Lưu trạng thái hiện tại
  ctx.beginPath();
  ctx.lineWidth = 2;
  ctx.textBaseline = "alphabetic";
  // ctx.fillStyle = type === 1 ? "#E74C3C" : "#3498DB";
  ctx.font = "16px Comic Sans MS";
  switch (type) {
    case 1:
      ctx.fillStyle = "#E74C3C";
      ctx.fillStyle = "red";
      ctx.textAlign = "center";
      ctx.fillText(
        item?.auToAddMach ? "" : "x",
        leftColumnWidth + columnWidth / 2 + index * columnWidth,
        point + 3
      );
      break;
    case 2:
      ctx.fillStyle = "#3498DB";
      ctx.arc(
        leftColumnWidth + columnWidth / 2 + index * columnWidth,
        point,
        RADIUS_POINTER,
        0,
        item?.auToAddNhietDo ? 0 : 2 * Math.PI
      );
      break;
    case 3:
      ctx.font = "20px Comic Sans MS";
      ctx.fillStyle = "#5cffbe";
      ctx.textAlign = "center";
      ctx.fillText(
        "♦",
        leftColumnWidth + columnWidth / 2 + index * columnWidth,
        point + 3
      );
      break;
    case 4:
      ctx.font = "16px Comic Sans MS";
      ctx.fillStyle = "#ac5fff";
      ctx.textAlign = "center";
      ctx.fillText(
        "▲",
        leftColumnWidth + columnWidth / 2 + index * columnWidth,
        point + 3
      );
      break;
    case 5: //huyết áp trung bình
      ctx.font = "16px Comic Sans MS";
      ctx.fillStyle = "#7C4DFF";
      ctx.textAlign = "center";
      ctx.fillText(
        "★",
        leftColumnWidth + columnWidth / 2 + index * columnWidth,
        point + 3
      );
      break;

    default:
      break;
  }

  ctx.fill();
  ctx.restore(); // Khôi phục trạng thái ban đầu
};

export const drawSurgery = (ctx, index, SIZE) => {
  ctx.beginPath();
  const bottom = 248;
  const lineWidth = 3;
  const top = 110;
  const top1 = 130;
  drawLine(
    ctx,
    {
      x:
        index * SIZE.columnWidth +
        SIZE.leftColumnWidth +
        SIZE.columnWidth / 2 -
        7,
      y: bottom,
    },
    {
      x:
        index * SIZE.columnWidth +
        SIZE.leftColumnWidth +
        SIZE.columnWidth / 2 +
        7,
      y: bottom,
    },
    lineWidth,
    [],
    "red"
  );
  drawLine(
    ctx,
    {
      x:
        index * SIZE.columnWidth +
        SIZE.leftColumnWidth +
        SIZE.columnWidth / 2 -
        7,
      y: bottom,
    },
    {
      x:
        index * SIZE.columnWidth +
        SIZE.leftColumnWidth +
        SIZE.columnWidth / 2 -
        7,
      y: top1,
    },
    lineWidth,
    [],
    "red"
  );
  drawLine(
    ctx,
    {
      x:
        index * SIZE.columnWidth +
        SIZE.leftColumnWidth +
        SIZE.columnWidth / 2 +
        7,
      y: bottom,
    },
    {
      x:
        index * SIZE.columnWidth +
        SIZE.leftColumnWidth +
        SIZE.columnWidth / 2 +
        7,
      y: top1,
    },
    lineWidth,
    [],
    "red"
  );
  drawLine(
    ctx,
    {
      x:
        index * SIZE.columnWidth +
        SIZE.leftColumnWidth +
        SIZE.columnWidth / 2 -
        7,
      y: top1,
    },
    {
      x:
        index * SIZE.columnWidth +
        SIZE.leftColumnWidth +
        SIZE.columnWidth / 2 -
        16,
      y: top1,
    },
    lineWidth,
    [],
    "red"
  );
  drawLine(
    ctx,
    {
      x:
        index * SIZE.columnWidth +
        SIZE.leftColumnWidth +
        SIZE.columnWidth / 2 +
        7,
      y: top1,
    },
    {
      x:
        index * SIZE.columnWidth +
        SIZE.leftColumnWidth +
        SIZE.columnWidth / 2 +
        16,
      y: top1,
    },
    lineWidth,
    [],
    "red"
  );
  drawLine(
    ctx,
    {
      x:
        index * SIZE.columnWidth +
        SIZE.leftColumnWidth +
        SIZE.columnWidth / 2 -
        16,
      y: top1,
    },
    {
      x: index * SIZE.columnWidth + SIZE.leftColumnWidth + SIZE.columnWidth / 2,
      y: top,
    },
    lineWidth,
    [],
    "red"
  );
  drawLine(
    ctx,
    {
      x:
        index * SIZE.columnWidth +
        SIZE.leftColumnWidth +
        SIZE.columnWidth / 2 +
        16,
      y: top1,
    },
    {
      x: index * SIZE.columnWidth + SIZE.leftColumnWidth + SIZE.columnWidth / 2,
      y: top,
    },
    lineWidth,
    [],
    "red"
  );
};

function measureText(text, font, columnWidth) {
  const span = document.createElement("span");
  span.appendChild(document.createTextNode(text));

  Object.assign(span.style, {
    font: font,
    lineHeight: "1.2", // Tăng line-height để tránh text bị cắt
    margin: "0",
    padding: "2px", // Thêm padding để tránh text bị cắt
    border: "0",
    ...(columnWidth
      ? { width: columnWidth + "px", display: "block", whiteSpace: "normal" }
      : { whiteSpace: "nowrap" }),
  });

  document.body.appendChild(span);
  const { width, height } = span.getBoundingClientRect();
  span.remove();

  // Tăng offset để fix chữ HOA và các ký tự đặc biệt bị cắt
  const offsetFix = 1; // Giảm offset vì đã thêm padding

  return { width, height: height - offsetFix };
}

export const calculateHeightByKey = (data, isNoiTru, key) => {
  let result = 0;
  if (isArray(data, true)) {
    let values = data.map((i) => i[key]).filter(Boolean);
    let maxValue = (values || []).sort((a, b) => b.length - a.length)[0];
    if (maxValue) {
      // Tính số dòng cần thiết cho text dài nhất
      const words = maxValue.split(" ");
      const maxWidth = 40 - 4; // columnWidth - margin
      let lines = 1;
      let currentLineWidth = 0;

      for (let word of words) {
        const wordWidth = measureText(word, "10px Times New Roman", null).width;
        const spaceWidth = measureText(" ", "10px Times New Roman", null).width;

        if (currentLineWidth + wordWidth > maxWidth && currentLineWidth > 0) {
          lines++;
          currentLineWidth = wordWidth;
        } else {
          currentLineWidth +=
            wordWidth + (currentLineWidth > 0 ? spaceWidth : 0);
        }
      }

      // Tính chiều cao với line spacing
      const fontSize = 10;
      const lineSpacing = fontSize * 1.2;
      result = lines * lineSpacing + 4; // thêm padding
    }
  }
  if (isNoiTru && key === "tenDvKham") {
    return 0;
  }
  if (key === "tenKhoaChiDinh") {
    result += 10;
  }
  result = result < 45 ? 45 : result;
  return Math.round(result);
};

export const drawTextMultipleLine = (
  ctx,
  text,
  from,
  color,
  fontSize = 12,
  SIZE
) => {
  if (!ctx || !text || typeof text !== "string") return;

  ctx.save(); // Lưu trạng thái hiện tại
  ctx.font = `${fontSize}px Times New Roman`;
  ctx.fillStyle = color || "black";
  ctx.textAlign = "start";
  ctx.textBaseline = "top"; // để from.y = mép trên

  let lines = [[]];
  let lineCount = 0;
  const words = text.split(" ");

  // Tăng margin để tránh text bị cắt
  const margin = 4; // margin từ mép cột
  const maxWidth = SIZE.columnWidth - margin;

  // wrap theo chiều rộng
  for (let i = 0; i < words.length; i++) {
    const word = words[i];
    const currentLine = lines[lineCount].join(" ");
    const testLine = currentLine ? currentLine + " " + word : word;

    // dùng measureText DOM-based
    const { width } = measureText(testLine, ctx.font, null);

    console.log("🚀 KhoaMilan -> width", width);
    console.log('🚀 KhoaMilan -> ', );

    if (width > maxWidth) {
      // Nếu từ đơn lẻ vẫn quá dài, cắt từ
      if (lines[lineCount].length === 0) {
        lines[lineCount] = [word];
        lineCount++;
        lines[lineCount] = [];
      } else {
        lineCount++;
        lines[lineCount] = [word];
      }
    } else {
      lines[lineCount].push(word);
    }
  }

  // Tăng line spacing để text không bị chồng lên nhau
  const lineSpacing = fontSize * 1.2;

  // vẽ từng dòng
  for (let l = 0; l < lines.length; l++) {
    const line = lines[l].join(" ");
    if (line.trim()) {
      // Chỉ vẽ dòng có nội dung
      ctx.fillText(line, from.x, from.y + l * lineSpacing);
    }
  }
  ctx.restore(); // Khôi phục trạng thái ban đầu
};

export const drawText = (
  ctx,
  text,
  from,
  color,
  fontSize = 12,
  align,
  fontWeight = "normal",
  textBaseline = "alphabetic"
) => {
  if (ctx) {
    ctx.save(); // Lưu trạng thái hiện tại
    ctx.font = `${fontWeight} ${fontSize}px Times New Roman`;
    ctx.fillStyle = color || "black";
    if (align) ctx.textAlign = `${align}`;
    else ctx.textAlign = "start";
    ctx.textBaseline = textBaseline;
    ctx.fillText(text, from.x, from.y);
    ctx.restore(); // Khôi phục trạng thái ban đầu
  }
};

export const drawDate = (ctx, values, SIZE) => {
  let number = 0;
  let itemNbPhauThuat = null;
  let date = null;
  values.forEach((item, index) => {
    let ddMM = item.thoiGianThucHien && item.thoiGianThucHien.format("dd/MM");
    if (ddMM) {
      ddMM = item.thoiGianThucHien.format("dd/MM");

      if (item.thoiGianThucHien instanceof moment) {
        ddMM = item.thoiGianThucHien.format("DD/MM");
      }
    }
    try {
      if (ddMM) {
        drawText(ctx, ddMM, {
          x: SIZE.leftColumnWidth + index * SIZE.columnWidth + 5,
          y: 20,
        });
      }
      if (item.thoiGianThucHien) {
        drawText(ctx, item.thoiGianThucHien.format("HH:mm"), {
          x: SIZE.leftColumnWidth + index * SIZE.columnWidth + 5,
          y: 60,
        });
      }

      if (item.chiDinhTuLoaiDichVu === LOAI_DICH_VU.TIEP_DON) {
        drawText(
          ctx,
          t("title.tiepDon"),
          {
            x: SIZE.leftColumnWidth + index * SIZE.columnWidth + 2,
            y: 100,
          },
          "",
          10
        );
      }
      if (
        item.chiDinhTuLoaiDichVu === LOAI_DICH_VU.KHAM &&
        item.chiDinhTuDichVuId
      ) {
        drawTextMultipleLine(
          ctx,
          t("title.khamBenh"),
          {
            x: SIZE.leftColumnWidth + index * SIZE.columnWidth + 2,
            y: 85,
          },
          "black",
          10,
          SIZE
        );
      }

      if (item.ptTtId) {
        drawText(
          ctx,
          "(1)",
          {
            x: SIZE.leftColumnWidth + index * SIZE.columnWidth + 15,
            y: 100,
          },
          "red"
        );
        number = 1;
        itemNbPhauThuat = item;
        date = item.thoiGianThucHien?.ddmmyyyy();
      } else {
        if (itemNbPhauThuat) {
          if (item.thoiGianThucHien?.ddmmyyyy() !== date && item.id) {
            date = item.thoiGianThucHien?.ddmmyyyy();
            number++;
            drawText(
              ctx,
              `(${number})`,
              {
                x: SIZE.leftColumnWidth + index * SIZE.columnWidth + 15,
                y: 100,
              },
              "red"
            );
          }
        }
      }
    } catch (error) {}
  });
};

export const pointToValue = ({
  point,
  type,
  rowHeight,
  totalRow,
  headerHeight,
  bottomHeight,
}) => {
  const sumCanvasHeight = rowHeight * totalRow + headerHeight + bottomHeight;
  if (type === 1) {
    let value = ((sumCanvasHeight - bottomHeight - point) / rowHeight) * 2 + 30;
    return value;
  } else {
    let value =
      (sumCanvasHeight - bottomHeight - point) / rowHeight / 10 + 34.5;
    return value;
  }
};

export const valueToPoint = ({
  value,
  type,
  rowHeight,
  totalRow = 75,
  headerHeight = 0,
  bottomHeight = 0,
  startValue,
  minValueHa,
  maxValueHa,
  minValueSpo2,
  maxValueSpo2,
}) => {
  const heightBody = rowHeight * totalRow;
  // 518
  const canvasHeight = heightBody + headerHeight + bottomHeight;
  let point = null;
  switch (type) {
    case 1:
      point =
        canvasHeight -
        bottomHeight +
        ((startValue - value) * rowHeight) / 2 +
        1.5;
      break;
    case 2:
      point =
        canvasHeight - bottomHeight + (startValue - value) * rowHeight * 10;
      break;
    case 3:
      const pxPerUnit = canvasHeight / (maxValueHa - minValueHa); // = 2.25
      point = (maxValueHa - value) * pxPerUnit;
      point = clamp(point, 0, heightBody);
      break;
    case 4:
      const range = maxValueSpo2 - minValueSpo2; // = 18
      const pxPerUnitSpo2 = canvasHeight / range;
      point = (maxValueSpo2 - value) * pxPerUnitSpo2 + headerHeight;
      point = clamp(point, 10, canvasHeight);
      break;
    case 5:
      point =
        canvasHeight -
        bottomHeight +
        (startValue - value) * rowHeight * 2 +
        1.5;
      break;
    default:
      break;
  }
  return point;
};

export const calculatorPosition = (count, value, res) => {
  if (count < 0) {
    return res;
  }
  return count * value + res;
};

export function drawSquare({
  ctx,
  index,
  from,
  height,
  color = "244,208,63",
  leftColumnWidth = 0,
  columnWidth,
}) {
  ctx.save(); // Lưu trạng thái hiện tại
  //random rgba colour
  ctx.fillStyle = `rgba(${color},0.5)`;
  ctx.fillRect(
    leftColumnWidth + index * columnWidth,
    from,
    columnWidth,
    height
  );
  ctx.restore(); // Khôi phục trạng thái ban đầu
}
export function drawBgColunm({
  ctx,
  index,
  from,
  height,
  color = "244,208,63",
  leftColumnWidth = 0,
  columnWidth,
}) {
  ctx.save(); // Lưu trạng thái hiện tại
  ctx.fillStyle = `rgba(${color},0.5)`;
  ctx.fillRect(
    leftColumnWidth + index * columnWidth,
    from,
    columnWidth,
    height
  );
  ctx.restore(); // Khôi phục trạng thái ban đầu
}

export function handleBloodPressure(values) {
  const bloodPressure = {
    huyetApTamThu: 0,
    huyetApTamTruong: 0,
  };
  if (!values) {
    return bloodPressure;
  }
  const splitValues = values.split("/");
  bloodPressure.huyetApTamThu = splitValues[0];
  bloodPressure.huyetApTamTruong = splitValues[1];
  return bloodPressure;
}
const renderStyle = (key, value, listThietLapGiaTriCSS, thongTinBenhNhan) => {
  let color = "black";
  let fontWeight = "normal";
  const { ngaySinh, gioiTinh } = thongTinBenhNhan || {};
  let listData = isArray(listThietLapGiaTriCSS, true)
    ? listThietLapGiaTriCSS.filter((i) => i.active)
    : null;
  if (isArray(listData, true)) {
    let thietLap = listData.find((i) => {
      let tuoi = ngaySinh ? moment().diff(ngaySinh, "years") : -1;
      return (
        i.tenCss === key &&
        i.gioiTinh === gioiTinh &&
        tuoi <= i.denTuoi &&
        tuoi >= i.tuTuoi
      );
    });
    if (thietLap) {
      const {
        giaTriToiDa,
        giaTriToiThieu,
        giaTriVuotNguongToiDa,
        giaTriVuotNguongToiThieu,
      } = thietLap || {};
      if (giaTriToiThieu <= value && value <= giaTriToiDa) {
        color = "black";
      }
      if (value > giaTriToiDa) {
        color = "#E74C3C";
      }
      if (value < giaTriToiThieu) {
        color = "green";
      }
      if (value > giaTriVuotNguongToiDa) {
        fontWeight = "bold";
      }
      if (value < giaTriVuotNguongToiThieu) {
        fontWeight = "bold";
      }
    }
  }

  return { color, fontWeight };
};

const caculateXposition = (index, SIZE) => {
  return index * SIZE.columnWidth + SIZE.leftColumnWidth + SIZE.columnWidth / 2;
};

export function drawValueFooter({
  marginTop,
  ctxFooter,
  index,
  item,
  values,
  moreValueIds,
  SIZE,
  isPrint,
  listNhomMau,
  listAcvpu,
  isNoiTru,
  heightTenDvkham = 0,
  heightTenKhoaChiDinh = 0,
  thongTinBenhNhan,
  dataMA_CSS_VONG_CANH_TAY,
  dataHIEN_THI_TRUONG_SINH_HIEU,
  listAllChiSoSong,
  listChiSoSongMacDinh,
  listChiSoSongKhac,
  footer,
  listThietLapGiaTriCSS,
}) {
  const _isPrint = isPrint || isNoiTru;
  const bmiVct = dataMA_CSS_VONG_CANH_TAY?.eval();
  let _bmiVct;
  if (bmiVct) {
    let dsChiSoSongKhac = (values[index].dsChiSoSongKhac || []).reduce(
      (acc, cur) => {
        let idx = listAllChiSoSong.findIndex((i) => i.id === cur.chiSoSongId);
        if (idx > -1) {
          acc.push({
            ...cur,
            ma: listAllChiSoSong[idx].ma,
          });
        }
        return acc;
      },
      []
    );
    _bmiVct =
      item.bmiVct ||
      dsChiSoSongKhac.find((i) => i.ma === dataMA_CSS_VONG_CANH_TAY)?.bmiVct;
  }
  if (!_isPrint) {
    if (item.mach && item.mach !== "null") {
      const { color, fontWeight } = renderStyle(
        "mach",
        item.mach,
        item.thietLap || listThietLapGiaTriCSS,
        thongTinBenhNhan
      );
      drawText(
        ctxFooter,
        item.mach,
        {
          x: caculateXposition(index, SIZE),
          y: marginTop + 20,
        },
        color,
        12,
        "center",
        fontWeight
      );
    }
    if (item.nhietDo && item.nhietDo !== "null") {
      const { color, fontWeight } = renderStyle(
        "nhietDo",
        item.nhietDo,
        item.thietLap,
        thongTinBenhNhan
      );
      drawText(
        ctxFooter,
        item.nhietDo,
        {
          x: caculateXposition(index, SIZE),
          y: marginTop + 65,
        },
        color,
        12,
        "center",
        fontWeight
      );
    }
  }
  let _filterChiSoSongMacDinh = footer
    ? listChiSoSongMacDinh.filter((i) => footer.some((j) => j.id === i.id))
    : listChiSoSongMacDinh;
  const moreValueIdsFilter = footer
    ? moreValueIds.filter((i) => footer.some((j) => j.id === i))
    : moreValueIds;
  let chiSoSongCoBan = footer ? footer.some((i) => i.index <= 6) : true;

  if (chiSoSongCoBan) {
    if (item.huyetApTamThu) {
      const { color, fontWeight } = renderStyle(
        "huyetApTamThu",
        item.huyetApTamThu,
        item.thietLap,
        thongTinBenhNhan
      );
      drawText(
        ctxFooter,
        `${item.huyetApTamThu}`,
        {
          x: index * SIZE.columnWidth + SIZE.leftColumnWidth + 2,
          y: marginTop + (_isPrint ? (isPrint ? 25 : 20) : 110),
        },
        color,
        11,
        "left",
        fontWeight
      );
    }
    if (item.huyetApTamTruong && item.huyetApTamThu) {
      const { color, fontWeight } = renderStyle(
        "huyetApTamTruong",
        item.huyetApTamTruong,
        item.thietLap,
        thongTinBenhNhan
      );
      const width1 =
        measureText(`${item.huyetApTamThu}`, ctxFooter.font).width - 4;
      drawText(
        ctxFooter,
        `/`,
        {
          x: index * SIZE.columnWidth + SIZE.leftColumnWidth + 2 + width1,
          y: marginTop + (_isPrint ? (isPrint ? 25 : 20) : 110),
        },
        "black",
        11,
        "left"
      );
      const width2 =
        measureText(`${item.huyetApTamThu}/`, ctxFooter.font).width - 4;
      drawText(
        ctxFooter,
        `${item.huyetApTamTruong}`,
        {
          x: index * SIZE.columnWidth + SIZE.leftColumnWidth + 2 + width2,
          y: marginTop + (_isPrint ? (isPrint ? 25 : 20) : 110),
        },
        color,
        11,
        "left",
        fontWeight
      );
    }
    if (item.nhipTho) {
      const troTho =
        item.troTho === TRO_THO.BOP_BONG
          ? "BB"
          : item.troTho === TRO_THO.MAY_THO
          ? "TM"
          : null;
      const { color, fontWeight } = renderStyle(
        "nhipTho",
        item.nhipTho,
        item.thietLap,
        thongTinBenhNhan
      );
      let fromY = marginTop + (_isPrint ? (isPrint ? 70 : 65) : 155);
      if (troTho) {
        fromY = marginTop + (_isPrint ? 60 : 145);
      }

      drawText(
        ctxFooter,
        item.nhipTho,
        {
          x: caculateXposition(index, SIZE),
          y: fromY,
        },
        color,
        12,
        "center",
        fontWeight
      );
      if (troTho) {
        drawText(
          ctxFooter,
          troTho,
          {
            x: caculateXposition(index, SIZE),
            y: marginTop + (_isPrint ? 80 : 165),
          },
          "black",
          12,
          "center"
        );
      }
    }

    if (item.canNang) {
      const { color, fontWeight } = renderStyle(
        "canNang",
        item.canNang,
        item.thietLap,
        thongTinBenhNhan
      );
      drawText(
        ctxFooter,
        item.canNang,
        {
          x: caculateXposition(index, SIZE),
          y: marginTop + (_isPrint ? (isPrint ? 115 : 110) : 200),
        },
        color,
        12,
        "center",
        fontWeight
      );
    }
  }
  if (!_isPrint) {
    if (item.chieuCao) {
      const { color, fontWeight } = renderStyle(
        "chieuCao",
        item.chieuCao,
        item.thietLap,
        thongTinBenhNhan
      );
      drawText(
        ctxFooter,
        item.chieuCao,
        {
          x: caculateXposition(index, SIZE),
          y: marginTop + (_isPrint ? 165 : 245),
        },
        color,
        12,
        "center",
        fontWeight
      );
    }
    if (item.nhomMau) {
      let tenNhomMau = listNhomMau?.find((i) => i.id === +item.nhomMau)?.ten;
      if (tenNhomMau) {
        if (
          measureText(tenNhomMau, ctxFooter.font).width >
          SIZE.columnWidth - 4 // Tăng margin để tránh text bị cắt
        ) {
          drawTextMultipleLine(
            ctxFooter,
            tenNhomMau,
            {
              x: index * SIZE.columnWidth + SIZE.leftColumnWidth + 2, // Đơn giản hóa positioning
              y: marginTop + 265,
            },
            "black",
            10,
            SIZE
          );
        } else {
          drawText(
            ctxFooter,
            tenNhomMau,
            {
              x: caculateXposition(index, SIZE),
              y: marginTop + 290,
            },
            "black",
            12,
            "center"
          );
        }
      }
    }
    if (item.spo2) {
      const { color, fontWeight } = renderStyle(
        "spo2",
        item.spo2,
        item.thietLap,
        thongTinBenhNhan
      );
      drawText(
        ctxFooter,
        item.spo2,
        {
          x: caculateXposition(index, SIZE),
          y: marginTop + 335,
        },
        color,
        12,
        "center",
        fontWeight
      );
    }
    if (item.bmi) {
      drawText(
        ctxFooter,
        item.bmi,
        {
          x: caculateXposition(index, SIZE),
          y: marginTop + 380,
        },
        "black",
        12,
        "center"
      );
    }
    if (item.tenPhanLoaiBmi) {
      if (
        measureText(item.tenPhanLoaiBmi, ctxFooter.font).width >
        SIZE.columnWidth - 4 // Tăng margin để tránh text bị cắt
      ) {
        drawTextMultipleLine(
          ctxFooter,
          item.tenPhanLoaiBmi,
          {
            x: index * SIZE.columnWidth + SIZE.leftColumnWidth + 2, // Tăng margin
            y: marginTop + 400,
          },
          "black",
          12,
          SIZE
        );
      } else {
        drawText(
          ctxFooter,
          item.tenPhanLoaiBmi,
          {
            x: caculateXposition(index, SIZE),
            y: marginTop + 425,
          },
          "black",
          12,
          "center"
        );
      }
    }
    if (item.moTaPhanLoaiBmi) {
      let daiBmi = item.moTaPhanLoaiBmi.split("-");
      daiBmi.forEach((el, i) => {
        drawText(
          ctxFooter,
          i < daiBmi.length - 1 ? `${el} -` : el,
          {
            x: caculateXposition(index, SIZE),
            y: marginTop + 460 + 15 * i,
          },
          "black",
          12,
          "center"
        );
      });
    }
  }

  if (bmiVct && chiSoSongCoBan) {
    if (_bmiVct) {
      drawText(
        ctxFooter,
        _bmiVct,
        {
          x: caculateXposition(index, SIZE),
          y: marginTop + (_isPrint ? (isPrint ? 160 : 155) : 510),
        },
        "black",
        12,
        "center"
      );
    }
  }
  if (item.tenDvKham && !isPrint) {
    drawTextMultipleLine(
      ctxFooter,
      item.tenDvKham,
      {
        x: index * SIZE.columnWidth + SIZE.leftColumnWidth + 2, // Tăng margin
        y: marginTop + (bmiVct ? 535 : 490),
      },
      "black",
      10,
      SIZE
    );
  }

  let _top = _isPrint
    ? marginTop + (bmiVct ? 140 : 95) + heightTenDvkham
    : marginTop + (bmiVct ? 500 + heightTenDvkham : 500 + heightTenDvkham);

  if (_isPrint && isArray(_filterChiSoSongMacDinh, true)) {
    let dsChiSoSongKhac = values[index].dsChiSoSongKhac || [];

    // _top = _top - 45;

    if (!chiSoSongCoBan) {
      _top = marginTop;
    }
    for (let i = 0; i < _filterChiSoSongMacDinh.length; i++) {
      const filterItem = _filterChiSoSongMacDinh[i];
      if (chiSoSongCoBan || i !== 0) {
        _top += 45;
      }
      const temp = dsChiSoSongKhac.find(
        (item) => item.chiSoSongId === filterItem.id
      );
      if (temp) {
        drawText(
          ctxFooter,
          temp.giaTri,
          {
            x: caculateXposition(index, SIZE),
            y: _top + (isPrint ? 20 : 15),
          },
          "black",
          12,
          "center"
        );
      }
    }
  }

  let top = marginTop + (_isPrint ? (!chiSoSongCoBan ? 0 : 110) : 470);
  if (bmiVct && chiSoSongCoBan) {
    top += 45;
  }
  if (!_isPrint) {
    top += heightTenDvkham >= 45 ? heightTenDvkham : 45;
  }
  for (let i = 0; i < _filterChiSoSongMacDinh.length; i++) {
    if (chiSoSongCoBan || i !== 0) {
      top += 45;
    } else if (i === 0) {
      top += 20;
    }
  }

  if (isNoiTru && dataHIEN_THI_TRUONG_SINH_HIEU?.eval()) {
    ["acvpu", "glasgow", "canhBaoSom"].forEach((key, idx) => {
      let value = item[key];
      if (key === "acvpu" && item[key]) {
        value = listAcvpu?.find((i) => i.id === +item[key])?.ten;
      }
      if (value) {
        let calculateY = top + 25 + idx * 45;
        drawTextMultipleLine(
          ctxFooter,
          value,
          {
            x: index * SIZE.columnWidth + SIZE.leftColumnWidth + 2,
            y: calculateY,
          },
          "black",
          12,
          "center"
        );
      }
    });

    top = top + 135;
  }

  for (let i = 0; i < moreValueIdsFilter.length; i++) {
    if (!_filterChiSoSongMacDinh.length && i === 0 && !chiSoSongCoBan) {
      top = top + 15;
    } else {
      top += 45;
    }
    let temp = (values[index].dsChiSoSongKhac || []).find((t) => {
      return t.chiSoSongId === moreValueIdsFilter[i];
    });
    if (temp) {
      drawText(
        ctxFooter,
        temp.giaTri,
        {
          x: caculateXposition(index, SIZE),
          y: top + (isPrint ? 5 : 0),
        },
        "black",
        12,
        "center"
      );
    }
  }

  if (item.tenNguoiThucHien) {
    let fromX = SIZE.leftColumnWidth + 5;
    if (index > 0) {
      fromX = index * SIZE.columnWidth + SIZE.leftColumnWidth + 5;
    }
    drawText(
      ctxFooter,
      item.surName || "",
      {
        x: fromX,
        y: top + (isPrint ? 35 : 75),
      },
      "black",
      12,
      "left",
      "normal",
      "top"
    );
    drawText(
      ctxFooter,
      item.firstName || "",
      {
        x: fromX,
        y: top + (isPrint ? 55 : 95),
      },
      "black",
      12,
      "left",
      "normal",
      "top"
    );
  }
  if (item.tenKhoaChiDinh && !isPrint) {
    drawTextMultipleLine(
      ctxFooter,
      item.tenKhoaChiDinh,
      {
        x: index * SIZE.columnWidth + SIZE.leftColumnWidth + 2, // Tăng margin
        y: top + 115,
      },
      "black",
      10,
      SIZE
    );
  }
}

export function drawValueBody({
  ctx,
  item,
  values,
  index,
  columnWidth,
  leftColumnWidth = 0,
  headerHeight,
  bottomHeight = 0,
  rowHeight,
  totalRow,
  startValueMach,
  startValueNhiet,
  startValueSpo2 = 68,
  startValueNhipTho = 10,
  preData = {},
  SIZE,
  isPrint = false,
  showSpo2,
  showNhipTho,
  minValueSpo2 = 68,
  maxValueSpo2 = 100,
}) {
  let preItem = null;
  let preIndex = null;

  if (index !== 0) {
    preIndex = preData.mach === undefined ? index - 1 : preData.mach;
    preItem = values[preIndex];

    const mach = isPrint
      ? preItem.mach >= 30 &&
        item.mach >= 30 &&
        preItem.mach <= 180 &&
        item.mach <= 180
      : preItem.mach && item.mach;

    if (mach) {
      drawLine(
        ctx,
        {
          x: preIndex * columnWidth + leftColumnWidth + columnWidth / 2,
          y: valueToPoint({
            value: preItem.mach,
            type: 1,
            rowHeight: rowHeight,
            totalRow,
            headerHeight,
            bottomHeight,
            startValue: startValueMach,
          }),
        },
        {
          x: index * columnWidth + leftColumnWidth + columnWidth / 2,
          y: valueToPoint({
            value: item.mach,
            type: 1,
            rowHeight,
            totalRow,
            headerHeight,
            bottomHeight,
            startValue: startValueMach,
          }),
        },
        2,
        [],
        "#E74C3C"
      );
    }
    // preIndex = (preData.nhietDo || preData.nhiet) === undefined ? index - 1 : (preData.nhietDo || preData.nhiet);
    // preItem = values[preIndex];

    const nhietDo = isPrint
      ? preItem.nhietDo >= 34.5 &&
        item.nhietDo >= 34.5 &&
        preItem.nhietDo <= 42 &&
        item.nhietDo <= 42
      : preItem.nhietDo && item.nhietDo;
    if (nhietDo) {
      preIndex =
        preData.nhietDo === undefined && preData.nhiet === undefined
          ? index - 1
          : preData.nhietDo || preData.nhiet || 0;
      preItem = values[preIndex];
      drawLine(
        ctx,
        {
          x: preIndex * columnWidth + leftColumnWidth + columnWidth / 2,
          y: valueToPoint({
            value: preItem.nhietDo,
            type: 2,
            rowHeight,
            totalRow,
            headerHeight,
            bottomHeight,
            startValue: startValueNhiet,
            item,
          }),
        },
        {
          x: index * columnWidth + leftColumnWidth + columnWidth / 2,
          y: valueToPoint({
            value: item.nhietDo,
            type: 2,
            rowHeight,
            totalRow,
            headerHeight,
            bottomHeight,
            startValue: startValueNhiet,
            item,
          }),
        },
        2,
        [],
        "#3498DB"
      );
    }

    // preIndex = (preData.nhietDo || preData.nhiet) === undefined ? index - 1 : (preData.nhietDo || preData.nhiet);
    // preItem = values[preIndex];

    const spo2 = isPrint
      ? preItem.spo2 >= 68 &&
        item.spo2 >= 68 &&
        preItem.spo2 <= 100 &&
        item.spo2 <= 100
      : preItem.spo2 && item.spo2;
    if (spo2 && showSpo2) {
      preIndex = preData.spo2 === undefined ? index - 1 : preData.spo2;
      preItem = values[preIndex];
      drawLine(
        ctx,
        {
          x: preIndex * columnWidth + leftColumnWidth + columnWidth / 2,
          y: valueToPoint({
            value: preItem.spo2,
            type: 4,
            rowHeight,
            totalRow,
            headerHeight,
            bottomHeight,
            item,
            minValueSpo2,
            maxValueSpo2,
          }),
        },
        {
          x: index * columnWidth + leftColumnWidth + columnWidth / 2,
          y: valueToPoint({
            value: item.spo2,
            type: 4,
            rowHeight,
            totalRow,
            headerHeight,
            bottomHeight,
            item,
            minValueSpo2,
            maxValueSpo2,
          }),
        },
        2,
        [],
        "#5cffbe"
      );
    }

    const nhipTho = isPrint
      ? preItem.nhipTho >= 10 &&
        item.nhipTho >= 10 &&
        preItem.nhipTho <= 50 &&
        item.nhipTho <= 50
      : preItem.nhipTho && item.nhipTho;
    if (nhipTho && showNhipTho) {
      preIndex = preData.nhipTho === undefined ? index - 1 : preData.nhipTho;
      preItem = values[preIndex];
      drawLine(
        ctx,
        {
          x: preIndex * columnWidth + leftColumnWidth + columnWidth / 2,
          y: valueToPoint({
            value: preItem.nhipTho,
            type: 5,
            rowHeight,
            totalRow,
            headerHeight,
            bottomHeight,
            startValue: startValueNhipTho,
            item,
          }),
        },
        {
          x: index * columnWidth + leftColumnWidth + columnWidth / 2,
          y: valueToPoint({
            value: item.nhipTho,
            type: 5,
            rowHeight,
            totalRow,
            headerHeight,
            bottomHeight,
            startValue: startValueNhipTho,
            item,
          }),
        },
        5,
        [],
        "#ac5fff"
      );
    }
  }

  if (isPrint ? item.mach >= 30 && item.mach <= 180 : item.mach) {
    drawPoint({
      ctx,
      index,
      point: valueToPoint({
        value: item.mach,
        type: 1,
        rowHeight,
        totalRow,
        headerHeight,
        bottomHeight,
        startValue: startValueMach,
      }),
      type: 1,
      columnWidth,
      leftColumnWidth,
      item,
    });
  }
  if (isPrint ? item.nhietDo >= 34.5 && item.nhietDo <= 42 : item.nhietDo) {
    drawPoint({
      ctx,
      index,
      point: valueToPoint({
        value: item.nhietDo,
        type: 2,
        rowHeight,
        totalRow,
        headerHeight,
        bottomHeight,
        startValue: startValueNhiet,
      }),
      type: 2,
      columnWidth,
      leftColumnWidth,
      item,
    });
  }
  if (
    (isPrint
      ? item.spo2 >= minValueSpo2 && item.spo2 <= maxValueSpo2
      : item.spo2) &&
    showSpo2
  ) {
    drawPoint({
      ctx,
      index,
      point: valueToPoint({
        value: item.spo2,
        type: 4,
        rowHeight,
        totalRow,
        headerHeight,
        bottomHeight,
        minValueSpo2,
        maxValueSpo2,
      }),
      type: 3,
      columnWidth,
      leftColumnWidth,
      item,
    });
  }
  if (
    (isPrint ? item.nhipTho >= 10 && item.nhipTho <= 50 : item.nhipTho) &&
    showNhipTho
  ) {
    drawPoint({
      ctx,
      index,
      point: valueToPoint({
        value: item.nhipTho,
        type: 5,
        rowHeight,
        totalRow,
        headerHeight,
        bottomHeight,
        startValue: startValueNhipTho,
      }),
      type: 4,
      columnWidth,
      leftColumnWidth,
      item,
    });
  }
  if (item.ptTtId) drawSurgery(ctx, index, SIZE);
}

export function drawBodyBloodPressure({
  ctx,
  item,
  index,
  huyetAp,
  canvasHeight,
  bottomHeight,
  headerHeight,
  rangeBloodPressure,
  columnWidth,
  bloodPressureHeight,
  bottomRangeBloodPressure, //khoảng cách từ giá trị cuối cùng của huyết app tới bottom
  rowHeight,
  totalRow,
  leftColumnWidth,
  getHuyetApTrungBinh,
  preData,
  values,
}) {
  if (huyetAp && rangeBloodPressure) {
    const handleValue = handleBloodPressure(huyetAp);
    const minValueHa = rangeBloodPressure[rangeBloodPressure.length - 1] - 20;
    const maxValueHa = rangeBloodPressure[0];
    if (handleValue.huyetApTamThu > minValueHa) {
      const pointY =
        handleValue.huyetApTamThu > rangeBloodPressure[0]
          ? 0
          : valueToPoint({
              value: handleValue.huyetApTamThu,
              type: 3,
              rowHeight,
              totalRow,
              headerHeight,
              bottomHeight,
              minValueHa,
              maxValueHa,
            });
      const pointLastY =
        handleValue.huyetApTamTruong < minValueHa // if diastolic smaller than smallestValue => bodyHeight
          ? canvasHeight - bottomHeight - headerHeight
          : valueToPoint({
              value: handleValue.huyetApTamTruong,
              type: 3,
              rowHeight,
              totalRow,
              headerHeight,
              bottomHeight,
              minValueHa,
              maxValueHa,
            });
      const color =
        handleValue.huyetApTamThu > 180 ? "255,140,0" : "244,208,63";
      if (getHuyetApTrungBinh) {
        const huyetApTrungBinh = getHuyetApTrungBinh?.(handleValue);
        const pointTrungBinh = valueToPoint({
          value: huyetApTrungBinh,
          type: 3,
          rowHeight,
          totalRow,
          headerHeight,
          bottomHeight,
          bloodPressureHeight,
          minValueHa,
          maxValueHa,
        });
        if (index !== 0) {
          let preIndex =
            preData.huyetAp === undefined ? index - 1 : preData.huyetAp;
          let preItem = values[preIndex];

          const huyetAp = preItem.huyetAp;
          const preHandleValue = handleBloodPressure(huyetAp);
          const preHuyetApTrungBinh = getHuyetApTrungBinh?.(preHandleValue);

          if (preHuyetApTrungBinh) {
            drawLine(
              ctx,
              {
                x: preIndex * columnWidth + leftColumnWidth + columnWidth / 2,
                y: valueToPoint({
                  value: preHuyetApTrungBinh,
                  type: 3,
                  rowHeight,
                  totalRow,
                  headerHeight,
                  bottomHeight,
                  bloodPressureHeight,
                  minValueHa,
                  maxValueHa,
                }),
              },
              {
                x: index * columnWidth + leftColumnWidth + columnWidth / 2,
                y: pointTrungBinh,
              },
              2,
              [],
              "#7C4DFF"
            );
          }
        }
        drawPoint({
          ctx,
          index,
          point: pointTrungBinh,
          type: 5,
          columnWidth,
          leftColumnWidth,
          item,
        });
      }
      drawSquare({
        ctx,
        index,
        from: pointY + headerHeight,
        height: pointLastY - pointY,
        color,
        leftColumnWidth,
        columnWidth,
      });
    }
  } else {
  }
}

export function drawLeftColumnBackground({
  ctx,
  canvasWidth,
  canvasHeight,
  sizeLeftItem,
  bottomHeight,
  SIZE,
}) {
  //line header 1
  drawLine(ctx, { x: 5, y: 0 }, { x: canvasWidth, y: 0 }, 1, []);
  //line header 2
  drawLine(ctx, { x: 5, y: 30 }, { x: canvasWidth, y: 30 }, 0.5, []);
  //line header 3
  drawLine(ctx, { x: 5, y: 80 }, { x: canvasWidth, y: 80 }, 0.5, []);

  //mid line col
  drawLine(ctx, { x: 5, y: 0 }, { x: 5, y: canvasHeight }, 0.5);
  //text col mach
  //mid line col
  drawLine(
    ctx,
    { x: sizeLeftItem, y: 30 },
    { x: sizeLeftItem, y: canvasHeight - bottomHeight },
    0.5
  );
  //text col nhiet
  NHIETS.forEach((item, index) => {
    drawText(ctx, item, {
      x: sizeLeftItem * 2 + 10,
      y: 5 + index * SIZE.rowHeight * 10 + SIZE.headerHeight,
    });
  });
  //mid line col
  drawLine(
    ctx,
    { x: sizeLeftItem * 2, y: 30 },
    { x: sizeLeftItem * 2, y: canvasHeight - bottomHeight },
    0.5
  );
  MACHS.forEach((item, index) => {
    drawText(ctx, item, {
      x: sizeLeftItem + 10,
      y: 5 + index * SIZE.rowHeight * 10 + SIZE.headerHeight,
    });
  });
  //line col end
  drawLine(
    ctx,
    { x: canvasWidth, y: 0 },
    { x: canvasWidth, y: canvasHeight },
    0.5
  );
}

export function drawLeftColumnBloodPressure(ctx, rangeBloodPressure, SIZE) {
  if (!ctx) {
    return;
  }
  if (!isEmpty(rangeBloodPressure)) {
    rangeBloodPressure.forEach((item, index) => {
      drawText(ctx, item, {
        x: 10,
        y: 5 + index * SIZE.rowHeight * 10 + SIZE.headerHeight,
      });
    });
  }
}

export function drawLeftColumnFooter({
  ctx,
  moreValueIds = [],
  vitalSignsCategories,
  canvasWidth,
  canvasHeight,
  bottomHeight,
  isPrint = false, // Phiếu theo dõi chức năng sống
  isNoiTru = false, // Chỉ số sống nội trú
  heightTenDvkham = 0,
  heightTenKhoaChiDinh = 0,
  dataMA_CSS_VONG_CANH_TAY,
  dataHIEN_THI_TRUONG_SINH_HIEU,
  listChiSoSongMacDinh, // list chỉ số sống có tick macDinh=true từ danh mục css
  footer,
}) {
  //line bottom
  let _isPrint = isNoiTru || isPrint; // là chỉ số sống nội trú hoặc khi in Phiếu theo dõi chức năng sống
  const bmiVct = dataMA_CSS_VONG_CANH_TAY?.eval();
  let plusHeightNgoaiTru = heightTenDvkham >= 45 ? heightTenDvkham - 45 : 0;
  let plusHeight = heightTenKhoaChiDinh >= 45 ? heightTenKhoaChiDinh - 45 : 0;
  let top = canvasHeight - bottomHeight;
  let x =
    (_isPrint
      ? bottomHeight - heightTenKhoaChiDinh + 45
      : bottomHeight - heightTenKhoaChiDinh - heightTenDvkham + 90) / 45;

  // Tính toán số items cuối cùng cho tenKhoaChiDinh (tương tự logic trong drawFooter)
  for (let i = 0; i <= x; i++) {
    const isLastItems = i >= Math.floor(x); // Items cuối cùng cho tenKhoaChiDinh
    if (!_isPrint) {
      // Ngoại trú: logic tương tự drawFooter - áp dụng cho tất cả dòng
      const isTenDvKhamArea = i >= (bmiVct ? 13 : 12); // Vùng tenDvKham

      let extraHeight = 0;
      if (isTenDvKhamArea && isLastItems) {
        extraHeight = plusHeightNgoaiTru + plusHeight; // Cả tenDvKham và tenKhoaChiDinh
      } else if (isTenDvKhamArea) {
        extraHeight = plusHeightNgoaiTru; // Chỉ tenDvKham
      } else if (isLastItems) {
        extraHeight = plusHeight; // Chỉ tenKhoaChiDinh
      }

      drawLine(
        ctx,
        { x: 5, y: top + i * 45 + extraHeight },
        {
          x: canvasWidth,
          y: top + i * 45 + extraHeight,
        },
        0.5,
        []
      );
    } else if (isNoiTru) {
      // Nội trú: chỉ áp dụng plusHeight cho tenKhoaChiDinh (items cuối)
      drawLine(
        ctx,
        { x: 5, y: top + i * 45 + (isLastItems ? plusHeight : 0) },
        {
          x: canvasWidth,
          y: top + i * 45 + (isLastItems ? plusHeight : 0),
        },
        0.5,
        []
      );
    } else {
      drawLine(
        ctx,
        { x: 5, y: top + i * 45 },
        {
          x: canvasWidth,
          y: top + i * 45,
        },
        0.5,
        []
      );
    }
  }
  if (!_isPrint) {
    drawText(ctx, `1. ${t("quanLyNoiTru.chiSoSong.nhipMach")}`, {
      x: 10,
      y: top + 15,
    });
    drawText(ctx, `${t("quanLyNoiTru.chiSoSong.lanPhut")}`, {
      x: 10,
      y: top + 35,
    });
    drawText(ctx, `2. ${t("quanLyNoiTru.chiSoSong.nhietDo")}`, {
      x: 10,
      y: top + 15 + 45,
    });
    drawText(ctx, `${t("sinhHieu.doC")}`, {
      x: 10,
      y: top + 35 + 45,
    });
  }
  let chiSoSongCoBan = footer ? footer.some((i) => i.index <= 6) : true;
  if (chiSoSongCoBan) {
    drawText(
      ctx,
      `${!_isPrint ? "3." : "1."} ${t("quanLyNoiTru.chiSoSong.huyetAp")}`,
      {
        x: 10,
        y: top + 15 + (!_isPrint ? 90 : 0),
      }
    );
    drawText(ctx, "(mmHg)", {
      x: 10,
      y: top + 35 + (!_isPrint ? 90 : 0),
    });
    drawText(
      ctx,
      `${!_isPrint ? "4." : "2."} ${t("quanLyNoiTru.chiSoSong.nhipTho")}`,
      {
        x: 10,
        y: top + 15 + (!_isPrint ? 135 : 45),
      }
    );
    drawText(ctx, `${t("quanLyNoiTru.chiSoSong.lanPhut")}`, {
      x: 10,
      y: top + 35 + (!_isPrint ? 135 : 45),
    });
    drawText(
      ctx,
      `${!_isPrint ? "5." : "3."} ${t("quanLyNoiTru.chiSoSong.canNang")}`,
      {
        x: 10,
        y: top + 15 + (!_isPrint ? 180 : 90),
      }
    );
    drawText(ctx, "(kg)", {
      x: 10,
      y: top + 35 + (!_isPrint ? 180 : 90),
    });
  }
  if (_isPrint && chiSoSongCoBan) {
    if (bmiVct) {
      drawText(ctx, `4. ${t("sinhHieu.bmiVct")}`, {
        x: 10,
        y: top + 15 + 145,
      });
    }
  }
  let _filterChiSoSongMacDinh = footer
    ? listChiSoSongMacDinh.filter((i) => footer.some((j) => j.id === i.id))
    : listChiSoSongMacDinh;
  const moreValueIdsFilter = footer
    ? moreValueIds.filter((i) => footer.some((j) => j.id === i))
    : moreValueIds;

  if (_isPrint && isArray(_filterChiSoSongMacDinh, true)) {
    let positionY = bmiVct ? 180 : 135;

    let baseIndex = bmiVct ? 5 : 4;
    if (!chiSoSongCoBan) {
      positionY = 0;
    }
    _filterChiSoSongMacDinh.forEach((item, index) => {
      let _idx = footer
        ? footer.find((i) => i.id === item.id)?.index
        : index + baseIndex;
      drawText(ctx, `${_idx}. ${item.ten}`, {
        x: 10,
        y: top + (item.donVi ? 15 : 30) + positionY + index * 45,
      });
      if (item.donVi) {
        drawText(ctx, `(${item.donVi})`, {
          x: 10,
          y: top + 35 + positionY + index * 45,
        });
      }
    });
  }
  if (!_isPrint) {
    drawText(ctx, `6. ${t("sinhHieu.vuiLongNhapChieuCao")}`, {
      x: 10,
      y: top + 15 + 225,
    });
    drawText(ctx, `(cm)`, {
      x: 10,
      y: top + 35 + 225,
    });
    drawText(ctx, `7. ${t("quanLyNoiTru.mau.nhomMau")}`, {
      x: 10,
      y: top + 15 + 270,
    });
    drawText(ctx, `8. ${t("sinhHieu.nhapSpo2")}`, {
      x: 10,
      y: top + 15 + 315,
    });
    drawText(ctx, "(%)", {
      x: 10,
      y: top + 35 + 315,
    });
    drawText(ctx, `9. ${t("sinhHieu.bmi")}`, {
      x: 10,
      y: top + 15 + 360,
    });
    drawText(ctx, `10. ${t("sinhHieu.mucDoBMI")}`, {
      x: 10,
      y: top + 15 + 405,
    });
    drawText(ctx, `11. ${t("sinhHieu.daiThamChieuBMI")}`, {
      x: 10,
      y: top + 15 + 450,
    });
    if (bmiVct) {
      drawText(ctx, `12. ${t("sinhHieu.bmiVct")}`, {
        x: 10,
        y: top + 15 + 495,
      });
      drawText(ctx, `13. ${t("sinhHieu.tenDvKham")}`, {
        x: 10,
        y: top + 15 + 540,
      });
    } else {
      drawText(ctx, `12. ${t("sinhHieu.tenDvKham")}`, {
        x: 10,
        y: top + 15 + 495,
      });
    }
  }

  let y = top + (_isPrint ? (chiSoSongCoBan ? 90 : 0) : 450);
  if (bmiVct && chiSoSongCoBan) {
    y = y + 45;
  }
  if (!_isPrint) {
    if (heightTenDvkham >= 45) {
      y = y + heightTenDvkham;
    } else {
      y = y + 45;
    }
  }

  if (_isPrint && isArray(_filterChiSoSongMacDinh, true)) {
    // y = y + _filterChiSoSongMacDinh.length * 45;
    _filterChiSoSongMacDinh.forEach((item, index) => {
      if (chiSoSongCoBan || index !== 0) {
        y += 45;
      } else if (index === 0) {
        y += 5;
      }
    });
  }

  if (isNoiTru && dataHIEN_THI_TRUONG_SINH_HIEU?.eval()) {
    let idx = bmiVct ? 5 : 4;
    idx = idx + _filterChiSoSongMacDinh.length - 1;
    drawText(ctx, `${idx + 1}. ${t("sinhHieu.acvpu")}`, {
      x: 10,
      y: y + 65,
    });
    drawText(ctx, `${idx + 2}. ${t("sinhHieu.glasgow")}`, {
      x: 10,
      y: y + 105,
    });
    drawText(ctx, `${idx + 3}. ${t("sinhHieu.canhBaoSom2")}`, {
      x: 10,
      y: y + 155,
    });

    y = y + 135;
  }

  moreValueIdsFilter.forEach((item, index) => {
    if (!_filterChiSoSongMacDinh.length && index === 0 && !chiSoSongCoBan) {
      y = y + 0;
    } else {
      y = y + 45;
    }
    let temp = (vitalSignsCategories || []).find((x) => {
      return x.id === item;
    });
    let baseIdx;

    if (_isPrint) {
      if (bmiVct) {
        baseIdx = isPrint ? 4 : 5;
      } else {
        baseIdx = isPrint ? 3 : 4;
      }
      if (dataHIEN_THI_TRUONG_SINH_HIEU?.eval()) {
        baseIdx += 3;
      }
    } else {
      baseIdx = bmiVct ? 14 : 13;
    }
    const idx = footer
      ? footer.find((i) => i.id === temp.id)?.index
      : baseIdx + _filterChiSoSongMacDinh.length;

    drawText(
      ctx,
      (() => {
        if (temp) {
          let text = idx + index + ". " + temp.ten;
          return text;
        }
        return "";
      })(),
      {
        x: 10,
        y: y + (temp?.donVi ? 15 : 30),
      }
    );
    if (temp?.donVi) {
      drawText(ctx, `(${temp.donVi})`, {
        x: 10,
        y: y + 35,
      });
    }
  });

  y = y + 30;

  drawText(ctx, t("cdha.dieuDuong"), {
    x: 10,
    y: y + 30 + (isPrint ? 0 : 50),
  });
  drawText(ctx, t("quanLyNoiTru.chiSoSong.kyVaGhiTen"), {
    x: 10,
    y: y + 50 + (isPrint ? 0 : 50),
  });
  drawText(ctx, t("common.khoa"), {
    x: 10,
    y: y + 85 + (isPrint ? 0 : 50),
  });
}
